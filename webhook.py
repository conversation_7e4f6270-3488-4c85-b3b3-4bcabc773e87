from flask import Flask, request, jsonify
import requests
import re
from datetime import datetime
import os
from dotenv import load_dotenv

# === LOAD ENVIRONMENT VARIABLES ===
load_dotenv()

app = Flask(__name__)

# === CONFIGURATION ===
VERIFY_TOKEN = os.getenv("META_VERIFY_TOKEN", "anand_travels")
WHATSAPP_ACCESS_TOKEN = os.getenv(
    "META_ACCESS_TOKEN",
    "EAAZAURkhjORYBPrFofrmlwmwjZCVkoTY3paY0iD5Q7ahOaMAG9jL9CpDZBwwASkXenAbah4o3nBSLu6syxmzjUQh51MPIZAkzb4EjzVKw80sZCebA5bZBzB6thIFqvXC8rRdTcMZBE65jpvowVaRxJ27H8312zeOpCWIhe9YCF40gLCMtI9fB0t8ZCl1ssuIngT9SZBGH91Vv3KjF97LJZAfv8vsMJsGIZCLqIAhnMkcjGXFAZDZD"
)
PHONE_NUMBER_ID = os.getenv("META_PHONE_NUMBER_ID", "879631015223442")
INDIAN_RAILWAY_API_KEY = os.getenv(
    "INDIAN_RAILWAY_API_KEY", "35ba772653msh319881d4d36374bp17b128jsna57344fbc26d"
)

# === HELPER FUNCTIONS ===
def gemini_agent(user_message):
    """Extract train number from user message using regex."""
    train_match = re.search(r"\b\d{4,5}\b", user_message)
    if not train_match:
        return {"error": "No train number found."}
    train_number = train_match.group()
    today = datetime.now().strftime("%Y%m%d")
    return {"train_number": train_number, "date": today}


def get_live_train_status(train_number, date):
    """Fetch live train running status from Indian Railway API."""
    url = (
        f"http://indianrailapi.com/api/v2/livetrainstatus/"
        f"apikey/{INDIAN_RAILWAY_API_KEY}/trainnumber/{train_number}/date/{date}/"
    )
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}


def format_output(api_response):
    """Format train status API response for WhatsApp message."""
    if "error" in api_response:
        return f"❌ Error: {api_response['error']}"
    train_name = api_response.get("TrainName", "Unknown Train")
    train_number = api_response.get("TrainNo", "N/A")
    position = api_response.get("Position", "No status available")
    return f"🚆 {train_name} ({train_number})\n📍 Status: {position}"


def send_whatsapp_message(to, message):
    """Send a WhatsApp message using the Meta API."""
    if not WHATSAPP_ACCESS_TOKEN or not PHONE_NUMBER_ID:
        print("⚠️ WhatsApp API credentials missing. Message not sent.")
        return

    url = f"https://graph.facebook.com/v17.0/{PHONE_NUMBER_ID}/messages"
    headers = {
        "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}",
        "Content-Type": "application/json",
    }
    data = {
        "messaging_product": "whatsapp",
        "to": to,
        "type": "text",
        "text": {"body": message},
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"✅ Message sent to {to}: {message}")
        print("📨 Meta API response:", response.status_code, response.text)
    except Exception as e:
        print("❌ Error sending WhatsApp message:", e)


# === ROUTES ===
@app.route("/", methods=["GET"])
def home():
    """Root route for local testing and health check."""
    return "🚀 WhatsApp Travel Assistant Webhook is running successfully!", 200


# --- META WEBHOOK VERIFICATION ---
@app.route("/webhook", methods=["GET"])
def verify_webhook():
    """Verify Meta webhook challenge."""
    mode = request.args.get("hub.mode")
    token = request.args.get("hub.verify_token")
    challenge = request.args.get("hub.challenge")

    if mode == "subscribe" and token == VERIFY_TOKEN:
        print("✅ Webhook verified successfully!")
        return challenge, 200
    else:
        print("❌ Webhook verification failed.")
        return "Verification failed", 403


# --- META WEBHOOK HANDLER ---
@app.route("/webhook", methods=["POST"])
def webhook():
    """Handle incoming WhatsApp messages."""
    data = request.get_json()

    try:
        message = data["entry"][0]["changes"][0]["value"]["messages"][0]
        user_text = message["text"]["body"]
        from_number = message["from"]

        print(f"📩 Message received from {from_number}: {user_text}")

        parsed = gemini_agent(user_text)
        if "error" in parsed:
            reply = "Please provide a valid train number (e.g., 12951)."
        else:
            api_data = get_live_train_status(parsed["train_number"], parsed["date"])
            reply = format_output(api_data)

        send_whatsapp_message(from_number, reply)
    except Exception as e:
        print("❌ Error processing webhook:", e)

    return jsonify(success=True), 200


# === LOCAL TESTING ENDPOINTS ===
@app.route("/test", methods=["GET"])
def test_endpoint():
    """Quick local status check."""
    return jsonify(
        {
            "status": "running",
            "message": "WhatsApp Bot is active and ready for testing",
            "endpoints": {
                "root": "/",
                "webhook_verify": "/webhook?hub.mode=subscribe&hub.verify_token=anand_travels&hub.challenge=test123",
                "test_train": "/test/train/12951",
                "test_message": "/test/message",
            },
        }
    ), 200


@app.route("/test/train/<train_number>", methods=["GET"])
def test_train_status(train_number):
    """Test train status retrieval."""
    today = datetime.now().strftime("%Y%m%d")
    api_data = get_live_train_status(train_number, today)
    formatted_reply = format_output(api_data)

    return jsonify(
        {
            "train_number": train_number,
            "date": today,
            "api_response": api_data,
            "formatted_message": formatted_reply,
        }
    ), 200


@app.route("/test/message", methods=["POST"])
def test_message_processing():
    """Simulate incoming WhatsApp message for testing."""
    data = request.get_json()

    if not data or "message" not in data:
        return jsonify(
            {"error": "Please send JSON: {'message': 'train 12951'}"}
        ), 400

    user_text = data["message"]
    parsed = gemini_agent(user_text)
    if "error" in parsed:
        reply = "Please provide a valid train number (e.g., 12951)."
    else:
        api_data = get_live_train_status(parsed["train_number"], parsed["date"])
        reply = format_output(api_data)

    return jsonify(
        {
            "input_message": user_text,
            "parsed_data": parsed,
            "reply": reply,
        }
    ), 200


# === START SERVER ===
if __name__ == "__main__":
    port = int(os.getenv("PORT", 5000))
    debug_mode = os.getenv("FLASK_DEBUG", "False").lower() == "true"

    print("\n==============================================")
    print(f"🚀 Starting WhatsApp Bot on http://localhost:{port}")
    print(f"🔧 Debug mode: {debug_mode}")
    print(f"🧪 Test endpoints: http://localhost:{port}/test")
    print("==============================================\n")

    app.run(host="0.0.0.0", port=port, debug=debug_mode)
