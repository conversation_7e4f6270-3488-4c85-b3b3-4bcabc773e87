# WhatsApp Travel Bot - Localhost Testing

A WhatsApp bot that provides live train status information using Indian Railway API.

## 🚀 Quick Start for Localhost Testing

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

The `.env` file contains all necessary configuration. You can modify it if needed:

```bash
# Edit .env file to update tokens if necessary
META_VERIFY_TOKEN=anand_travels
META_ACCESS_TOKEN=your_whatsapp_access_token
META_PHONE_NUMBER_ID=your_phone_number_id
INDIAN_RAILWAY_API_KEY=your_railway_api_key
PORT=5000
FLASK_ENV=development
FLASK_DEBUG=True
```

### 3. Start the Bot

```bash
python webhook.py
```

The bot will start on `http://localhost:5000`

### 4. Test the Bot

Run the test script to verify everything is working:

```bash
python test_bot.py
```

## 🧪 Testing Endpoints

Once the bot is running, you can test these endpoints:

### Basic Status
- **GET** `http://localhost:5000/` - Check if bot is running
- **GET** `http://localhost:5000/test` - Get available test endpoints

### Webhook Testing
- **GET** `http://localhost:5000/webhook?hub.mode=subscribe&hub.verify_token=anand_travels&hub.challenge=test123`
  - Tests webhook verification (required for WhatsApp setup)

### Train Status Testing
- **GET** `http://localhost:5000/test/train/12951` - Test train status lookup directly
- Replace `12951` with any valid Indian train number

### Message Processing Testing
- **POST** `http://localhost:5000/test/message`
- Send JSON: `{"message": "What is the status of train 12951?"}`
- Tests the complete message processing pipeline

## 📱 Manual Testing Examples

### Using curl:

```bash
# Test server status
curl http://localhost:5000/

# Test webhook verification
curl "http://localhost:5000/webhook?hub.mode=subscribe&hub.verify_token=anand_travels&hub.challenge=test123"

# Test train status
curl http://localhost:5000/test/train/12951

# Test message processing
curl -X POST http://localhost:5000/test/message \
  -H "Content-Type: application/json" \
  -d '{"message": "Check train 12951 status"}'
```

### Using a web browser:
- Open `http://localhost:5000/` to see if the bot is running
- Open `http://localhost:5000/test` to see available endpoints
- Open `http://localhost:5000/test/train/12951` to test train status

## 🔧 Features

- **Train Status Lookup**: Get live status of Indian trains
- **Message Parsing**: Extracts train numbers from natural language
- **WhatsApp Integration**: Ready for Meta WhatsApp Business API
- **Local Testing**: Complete testing suite for development

## 📋 API Response Format

The bot returns formatted messages like:
```
🚆 MUMBAI RAJDHANI (12951)
📍 Status: Train is running on time
```

## 🛠️ Development

- The bot uses Flask for the web server
- Environment variables are loaded from `.env` file
- Debug mode is enabled for development
- All sensitive data is stored in environment variables

## 🌐 Production Deployment

For production deployment:
1. Set `FLASK_DEBUG=False` in `.env`
2. Use a production WSGI server like Gunicorn
3. Set up proper webhook URL with Meta WhatsApp Business API
4. Ensure all environment variables are properly configured

## 📞 Support

If you encounter any issues during testing, check:
1. All dependencies are installed (`pip install -r requirements.txt`)
2. The `.env` file has correct values
3. The Indian Railway API key is valid
4. Port 5000 is not being used by another application
