from flask import Flask, request, jsonify
import requests
import re
from datetime import datetime

app = Flask(__name__)

# --- CONFIG ---
VERIFY_TOKEN = "anand_travels"  # your Meta webhook verify token
WHATSAPP_ACCESS_TOKEN = "EAAZAURkhjORYBPrFofrmlwmwjZCVkoTY3paY0iD5Q7ahOaMAG9jL9CpDZBwwASkXenAbah4o3nBSLu6syxmzjUQh51MPIZAkzb4EjzVKw80sZCebA5bZBzB6thIFqvXC8rRdTcMZBE65jpvowVaRxJ27H8312zeOpCWIhe9YCF40gLCMtI9fB0t8ZCl1ssuIngT9SZBGH91Vv3KjF97LJZAfv8vsMJsGIZCLqIAhnMkcjGXFAZDZD"
PHONE_NUMBER_ID = "879631015223442"
INDIAN_RAILWAY_API_KEY = "35ba772653msh319881d4d36374bp17b128jsna57344fbc26d"

# --- GEMINI MOCK (parsing step) ---
def gemini_agent(user_message):
    train_match = re.search(r"\b\d{4,5}\b", user_message)
    if not train_match:
        return {"error": "No train number found."}
    train_number = train_match.group()
    today = datetime.now().strftime("%Y%m%d")
    return {"train_number": train_number, "date": today}

# --- CALL INDIAN RAILWAY API ---
def get_live_train_status(train_number, date):
    url = f"http://indianrailapi.com/api/v2/livetrainstatus/apikey/{INDIAN_RAILWAY_API_KEY}/trainnumber/{train_number}/date/{date}/"
    try:
        r = requests.get(url)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        return {"error": str(e)}

# --- FORMAT OUTPUT ---
def format_output(api_response):
    if "error" in api_response:
        return f"❌ Error: {api_response['error']}"
    train_name = api_response.get("TrainName", "Unknown Train")
    train_number = api_response.get("TrainNo", "N/A")
    position = api_response.get("Position", "No status available")
    return f"🚆 {train_name} ({train_number})\n📍 Status: {position}"

# --- SEND MESSAGE VIA META API ---
def send_whatsapp_message(to, message):
    url = f"https://graph.facebook.com/v17.0/{PHONE_NUMBER_ID}/messages"
    headers = {
        "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    data = {
        "messaging_product": "whatsapp",
        "to": to,
        "type": "text",
        "text": {"body": message}
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# --- VERIFY WEBHOOK (Meta setup) ---
@app.route("/webhook", methods=["GET"])
def verify():
    if request.args.get("hub.mode") == "subscribe" and request.args.get("hub.verify_token") == VERIFY_TOKEN:
        return request.args.get("hub.challenge")
    return "Verification failed", 403

# --- RECEIVE MESSAGES ---
@app.route("/webhook", methods=["POST"])
def webhook():
    data = request.get_json()
    try:
        message = data["entry"][0]["changes"][0]["value"]["messages"][0]
        user_text = message["text"]["body"]
        from_number = message["from"]

        # 1. Parse train info
        parsed = gemini_agent(user_text)
        if "error" in parsed:
            reply = "Please provide a valid train number (e.g. 12951)."
        else:
            # 2. Fetch live train status
            api_data = get_live_train_status(parsed["train_number"], parsed["date"])
            # 3. Format reply
            reply = format_output(api_data)

        # 4. Send message back to user
        send_whatsapp_message(from_number, reply)

    except Exception as e:
        print("Error:", e)

    return jsonify(success=True)

# --- RUN SERVER ---
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)
