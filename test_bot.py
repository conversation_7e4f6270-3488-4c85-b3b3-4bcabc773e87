#!/usr/bin/env python3
"""
Test script for WhatsApp Travel Bot
Run this to test the bot functionality locally
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def test_server_status():
    """Test if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Server Status: {response.status_code}")
        print(f"📝 Response: {response.text}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start the bot first with: python webhook.py")
        return False

def test_webhook_verification():
    """Test webhook verification endpoint"""
    try:
        url = f"{BASE_URL}/webhook?hub.mode=subscribe&hub.verify_token=anand_travels&hub.challenge=test123"
        response = requests.get(url)
        print(f"✅ Webhook Verification: {response.status_code}")
        print(f"📝 Challenge Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Webhook Verification Failed: {e}")
        return False

def test_train_status(train_number="12951"):
    """Test train status lookup"""
    try:
        response = requests.get(f"{BASE_URL}/test/train/{train_number}")
        print(f"✅ Train Status Test: {response.status_code}")
        data = response.json()
        print(f"🚆 Train: {train_number}")
        print(f"📅 Date: {data.get('date', 'N/A')}")
        print(f"💬 Formatted Message: {data.get('formatted_message', 'N/A')}")
        return True
    except Exception as e:
        print(f"❌ Train Status Test Failed: {e}")
        return False

def test_message_processing(message="What is the status of train 12951?"):
    """Test message processing"""
    try:
        payload = {"message": message}
        response = requests.post(f"{BASE_URL}/test/message", json=payload)
        print(f"✅ Message Processing Test: {response.status_code}")
        data = response.json()
        print(f"💬 Input: {data.get('input_message', 'N/A')}")
        print(f"🔍 Parsed: {data.get('parsed_data', 'N/A')}")
        print(f"📤 Reply: {data.get('reply', 'N/A')}")
        return True
    except Exception as e:
        print(f"❌ Message Processing Test Failed: {e}")
        return False

def test_endpoints_info():
    """Get test endpoints information"""
    try:
        response = requests.get(f"{BASE_URL}/test")
        print(f"✅ Test Endpoints Info: {response.status_code}")
        data = response.json()
        print(f"📋 Available endpoints:")
        for endpoint, url in data.get('endpoints', {}).items():
            print(f"   • {endpoint}: {BASE_URL}{url}")
        return True
    except Exception as e:
        print(f"❌ Test Endpoints Info Failed: {e}")
        return False

def main():
    print("🤖 WhatsApp Travel Bot - Local Testing")
    print("=" * 50)
    
    # Test server status first
    if not test_server_status():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Run all tests
    tests = [
        ("Test Endpoints Info", test_endpoints_info),
        ("Webhook Verification", test_webhook_verification),
        ("Train Status Lookup", lambda: test_train_status("12951")),
        ("Message Processing", lambda: test_message_processing("Check train 12951 status")),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your bot is ready for testing.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
